import React, { useState, useContext, useCallback, useEffect, useRef } from 'react';
import {
  TradingModuleContainer,
  TradingPanelContainer,
  TradingPanelHeader,
  InputContainer,
  AmountInput,
  ButtonsContainer,
  ActionButton,
  PositionsContainer,
  PositionsList,
  PositionItem,
  PositionDetails,
  PositionSymbol,
  PositionAmount,
  PositionSide,
  EmptyPositions,
  PositionPnl
} from '../styles/tradingPanel.styles';
import { AppContext } from '../contexts/app.context';
import { placeMarketOrder, fetchAccount, closePosition } from '../common/api';
import { useNotification } from '../hooks/useNotification';
import { EPositionSide, TPosition } from '../types';

interface TradingPanelProps {
  positions: TPosition[];
}

export const TradingPanel: React.FC<TradingPanelProps> = (props) => {
  // Input state
  const [amount, setAmount] = useState<string>('');

  // Loading states - explicitly initialized as false
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isPositionsLoading, setIsPositionsLoading] = useState<boolean>(false);
  const [isBuyLoading, setIsBuyLoading] = useState<boolean>(false);
  const [isReverseLoading, setIsReverseLoading] = useState<boolean>(false);
  const [isSellLoading, setIsSellLoading] = useState<boolean>(false);
  const [isCloseLoading, setIsCloseLoading] = useState<Record<string, boolean>>({});

  // Data states
  const [positions, setPositions] = useState<TPosition[]>([]);
  const [showPositions, setShowPositions] = useState<boolean>(false);
  const [availableMargin, setAvailableMargin] = useState<string>('');

  // Default position values - using more specific values to ensure proper positioning
  const defaultPosition = {
    bottom: '30%',
    left: '40%',
    top: 'auto' as const, // Clear any top value
    right: 'auto' as const // Clear any right value
  };

  // Dragging states - initialize from localStorage if available
  const [panelCoord, setPanelCoord] = useState(() => {
    const savedCoord = localStorage.getItem('tradingPanelCoord');
    return savedCoord ? JSON.parse(savedCoord) : { x: 0, y: 0 };
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Active/Idle state
  const [isActive, setIsActive] = useState(false);
  const [idleTimer, setIdleTimer] = useState<number | null>(null);
  const [isHovering, setIsHovering] = useState(false);

  const panelRef = useRef<HTMLDivElement>(null);

  // Context
  const { symbol } = useContext(AppContext);
  const { addNotification } = useNotification();

  // Format symbol for display (e.g., "btcusdt" -> "BTC")
  const formatSymbol = useCallback((symbolStr: string) => {
    return symbolStr.replace(/usdt$/i, '').toUpperCase();
  }, []);

  // Helper function to reset all loading states
  const resetAllLoadingStates = useCallback(() => {
    setIsBuyLoading(false);
    setIsSellLoading(false);
    setIsLoading(false);
    setIsPositionsLoading(false);
    setIsCloseLoading({});
  }, []);

  // Function to refresh account data (positions and available margin)
  const refreshPositions = useCallback(async () => {
    try {
      // We use a separate loading state for positions to avoid interfering with action-specific loading states
      const accountData = await fetchAccount();

      // Update positions
      setPositions(accountData.positions);

      // Update available margin
      setAvailableMargin(accountData.availableMargin);

      // Show drawer if there are positions, hide if empty
      setShowPositions(accountData.positions.length > 0);

      return accountData.positions;
    } catch (error: any) {
      console.error('Failed to fetch account data:', error);
      addNotification({
        message: `Failed to fetch account data: ${error.message || 'Unknown error'}`,
        type: 'error',
        duration: 5000
      });
      return [];
    }
  }, [addNotification]);

  // Load positions on initial render
  useEffect(() => {
    const loadInitialPositions = async () => {
      setIsPositionsLoading(true);
      await refreshPositions();
      setIsPositionsLoading(false);
    };

    loadInitialPositions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset loading states when symbol changes or component unmounts
  useEffect(() => {
    // Reset loading states immediately when symbol changes
    resetAllLoadingStates();

    return () => {
      // Cleanup function to reset all loading states on unmount
      resetAllLoadingStates();
    };
  }, [symbol, resetAllLoadingStates]);

  // Update positions when props change
  useEffect(() => {
    setPositions(props.positions);
  }, [props.positions]);

  // Functions to handle active/idle state
  const activatePanel = useCallback(() => {
    setIsActive(true);

    // Clear any existing idle timer
    if (idleTimer !== null) {
      window.clearTimeout(idleTimer);
    }

    // Only set an idle timer if not hovering
    if (!isHovering) {
      const timer = window.setTimeout(() => {
        // Only go idle if we're not hovering
        if (!isHovering) {
          setIsActive(false);
        }
      }, 3000); // 3 seconds of inactivity before going idle

      setIdleTimer(timer);
    }
  }, [idleTimer, isHovering]);

  // Reset idle timer on any interaction
  const resetIdleTimer = useCallback(() => {
    if (isActive) {
      // Only reset the timer if already active
      if (idleTimer !== null) {
        window.clearTimeout(idleTimer);
      }

      // Only set a new timer if not hovering
      if (!isHovering) {
        const timer = window.setTimeout(() => {
          // Only go idle if we're not hovering
          if (!isHovering) {
            setIsActive(false);
          }
        }, 3000); // 3 seconds of inactivity

        setIdleTimer(timer);
      }
    }
  }, [isActive, idleTimer, isHovering]);

  // Drag handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Activate the panel
    activatePanel();

    if (panelRef.current) {
      setIsDragging(true);

      // Calculate the offset between mouse position and panel position
      const rect = panelRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  }, [activatePanel]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    // Reset idle timer when moving the panel
    if (isDragging) {
      resetIdleTimer();
    }

    if (isDragging && panelRef.current) {
      // Get panel dimensions
      const panelRect = panelRef.current.getBoundingClientRect();
      const panelWidth = panelRect.width;
      const panelHeight = panelRect.height;

      // Calculate new position based on mouse position and offset
      let newX = e.clientX - dragOffset.x;
      let newY = e.clientY - dragOffset.y;

      // Keep panel within viewport bounds
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Ensure panel stays within horizontal bounds
      if (newX < 0) newX = 0;
      if (newX + panelWidth > viewportWidth) newX = viewportWidth - panelWidth;

      // Ensure panel stays within vertical bounds
      if (newY < 0) newY = 0;
      if (newY + panelHeight > viewportHeight) newY = viewportHeight - panelHeight;

      const newCoord = { x: newX, y: newY };
      setPanelCoord(newCoord);

      // Save position to localStorage
      localStorage.setItem('tradingPanelCoord', JSON.stringify(newCoord));
    }
  }, [isDragging, dragOffset, resetIdleTimer]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Function to reset panel position to default
  const resetPosition = useCallback(() => {
    // Clear any existing event listeners
    if (isDragging) {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      setIsDragging(false);
    }

    // Reset position state
    setPanelCoord({ x: 0, y: 0 });

    // Remove from localStorage
    localStorage.removeItem('tradingPanelCoord');

    // Force a reflow to ensure the DOM is updated
    if (panelRef.current) {
      // This triggers a reflow
      void panelRef.current.offsetHeight;

      // Reset any inline styles that might be causing issues
      const style = panelRef.current.style;
      style.transform = '';
      style.transition = '';

      // Apply default position directly to the DOM element
      style.bottom = defaultPosition.bottom;
      style.left = defaultPosition.left;
      style.top = defaultPosition.top;
      style.right = defaultPosition.right;
    }

    // Activate the panel to ensure it's visible
    activatePanel();

    // Add a small delay to ensure all DOM updates are processed
    setTimeout(() => {
      if (panelRef.current) {
        // Trigger another reflow
        void panelRef.current.offsetHeight;
      }
    }, 0);
  }, [isDragging, handleMouseMove, handleMouseUp, activatePanel, defaultPosition]);

  // Add and remove event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Clean up idle timer when component unmounts
  useEffect(() => {
    return () => {
      if (idleTimer !== null) {
        window.clearTimeout(idleTimer);
      }
    };
  }, [idleTimer]);

  // Set panel to active initially, then transition to idle if not being hovered
  useEffect(() => {
    // Start active
    setIsActive(true);

    // Set timer to go idle after 3 seconds, but only if not being hovered
    const timer = window.setTimeout(() => {
      if (!isHovering) {
        setIsActive(false);
      }
    }, 3000);

    setIdleTimer(timer);

    return () => {
      if (timer) {
        window.clearTimeout(timer);
      }
    };
  }, [isHovering]);

  const handleAmountChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    // Activate panel when user interacts with input
    activatePanel();

    // Only allow numbers and decimal point
    const value = e.target.value;
    if (/^(\d*\.?\d*)$/.test(value) || value === '') {
      setAmount(value);
    }
  }, [activatePanel]);

  const handleBuy = useCallback(async () => {
    // Activate panel when user clicks buy
    activatePanel();

    if (!amount || isNaN(parseFloat(amount))) return;

    // Set loading states
    setIsBuyLoading(true);
    setIsLoading(true);

    // Safety timeout to reset loading state after 10 seconds
    const safetyTimeout = setTimeout(() => {
      resetAllLoadingStates();
      console.warn('Buy operation timed out after 10 seconds');
    }, 10000);

    try {
      const response = await placeMarketOrder({
        symbol,
        side: 'buy',
        usdAmount: parseFloat(amount)
      });

      // Only proceed if we got a valid response
      if (response && response.orderId) {
        addNotification({
          message: `Successfully bought ${response.cryptoAmount} ${formatSymbol(symbol)} for $${amount}`,
          type: 'success',
          duration: 3000
        });

        // Reset amount after successful action
        setAmount('');

        // Refresh positions
        setIsPositionsLoading(true);
        await refreshPositions();
        setIsPositionsLoading(false);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error: any) {
      console.error('Buy order error:', error);
      addNotification({
        message: `Failed to place buy order: ${error.message || 'Unknown error'}`,
        type: 'error',
        duration: 5000
      });
    } finally {
      // Clear the safety timeout
      clearTimeout(safetyTimeout);

      // Always reset loading states, even if there was an error
      setIsBuyLoading(false);
      setIsLoading(false);

      // Force a complete reset to ensure no visual artifacts remain
      setTimeout(() => resetAllLoadingStates(), 100);
    }
  }, [amount, symbol, addNotification, refreshPositions, formatSymbol, resetAllLoadingStates, activatePanel]);

  const handleSell = useCallback(async () => {
    // Activate panel when user clicks sell
    activatePanel();

    if (!amount || isNaN(parseFloat(amount))) return;

    // Set loading states
    setIsSellLoading(true);
    setIsLoading(true);

    // Safety timeout to reset loading state after 10 seconds
    const safetyTimeout = setTimeout(() => {
      resetAllLoadingStates();
      console.warn('Sell operation timed out after 10 seconds');
    }, 10000);

    try {
      const response = await placeMarketOrder({
        symbol,
        side: 'sell',
        usdAmount: parseFloat(amount)
      });

      // Only proceed if we got a valid response
      if (response && response.orderId) {
        addNotification({
          message: `Successfully sold ${response.cryptoAmount} ${formatSymbol(symbol)} for $${amount}`,
          type: 'success',
          duration: 3000
        });

        // Reset amount after successful action
        setAmount('');

        // Refresh positions
        setIsPositionsLoading(true);
        await refreshPositions();
        setIsPositionsLoading(false);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error: any) {
      console.error('Sell order error:', error);
      addNotification({
        message: `Failed to place sell order: ${error.message || 'Unknown error'}`,
        type: 'error',
        duration: 5000
      });
    } finally {
      // Clear the safety timeout
      clearTimeout(safetyTimeout);

      // Always reset loading states, even if there was an error
      setIsSellLoading(false);
      setIsLoading(false);

      // Force a complete reset to ensure no visual artifacts remain
      setTimeout(() => resetAllLoadingStates(), 100);
    }
  }, [amount, symbol, addNotification, refreshPositions, formatSymbol, resetAllLoadingStates, activatePanel]);

  const handleClosePosition = useCallback(async (positionSymbol: string) => {
    // Activate panel when user closes a position
    activatePanel();

    // Set loading state for this specific close button
    setIsCloseLoading(prev => ({ ...prev, [positionSymbol]: true }));

    // Safety timeout to reset loading state after 10 seconds
    const safetyTimeout = setTimeout(() => {
      setIsCloseLoading(prev => ({ ...prev, [positionSymbol]: false }));
      console.warn(`Close position operation for ${positionSymbol} timed out after 10 seconds`);
    }, 10000);

    try {
      const response = await closePosition(positionSymbol);

      // Only proceed if we got a valid response
      if (response && response.orderId) {
        addNotification({
          message: `Successfully closed ${formatSymbol(positionSymbol)} position`,
          type: 'success',
          duration: 3000
        });

        // After successful close, refresh positions
        setIsPositionsLoading(true);
        await refreshPositions();
        setIsPositionsLoading(false);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error: any) {
      console.error('Close position error:', error);
      addNotification({
        message: `Failed to close position: ${error.message || 'Unknown error'}`,
        type: 'error',
        duration: 5000
      });

      // Refresh positions anyway to ensure UI is in sync with server state
      try {
        setIsPositionsLoading(true);
        await refreshPositions();
      } catch (refreshError) {
        console.error('Failed to refresh positions after error:', refreshError);
      } finally {
        setIsPositionsLoading(false);
      }
    } finally {
      // Clear the safety timeout
      clearTimeout(safetyTimeout);

      // Always clear loading state for this specific close button
      setIsCloseLoading(prev => ({ ...prev, [positionSymbol]: false }));

      // Force a complete reset to ensure no visual artifacts remain
      setTimeout(() => resetAllLoadingStates(), 100);
    }
  }, [addNotification, refreshPositions, formatSymbol, resetAllLoadingStates, activatePanel]);

  const handleReverse = useCallback(async () => {
    // Activate panel when user clicks reverse
    activatePanel();

    if (!amount || isNaN(parseFloat(amount))) return;
    const position = positions.find(p => p.symbol === symbol)
    if (!position) return;

    // Set loading states
    setIsReverseLoading(true);
    setIsLoading(true);

    // Safety timeout to reset loading state after 10 seconds
    const safetyTimeout = setTimeout(() => {
      resetAllLoadingStates();
      console.warn('Reverse operation timed out after 10 seconds');
    }, 10000);

    try {
      const closeResponse = await closePosition(symbol);
      if (!closeResponse || !closeResponse.orderId) {
        throw new Error('Failed to close position');
      }

      const response = await placeMarketOrder({
        symbol,
        side: position.side === 'long' ? 'sell' : 'buy',
        usdAmount: parseFloat(amount)
      });

      // Only proceed if we got a valid response
      if (response && response.orderId) {
        addNotification({
          message: `Successfully reversed ${response.cryptoAmount} ${formatSymbol(symbol)} for $${amount}`,
          type: 'success',
          duration: 3000
        });

        // Reset amount after successful action
        setAmount('');

        // Refresh positions
        setIsPositionsLoading(true);
        await refreshPositions();
        setIsPositionsLoading(false);
      } else {
        throw new Error('Failed to place market order');
      }
    } catch (error: any) {
      console.error('Reverse order error:', error);
      addNotification({
        message: `Failed to place reverse order: ${error.message || 'Unknown error'}`,
        type: 'error',
        duration: 5000
      });
    } finally {
      // Clear the safety timeout
      clearTimeout(safetyTimeout);

      // Always reset loading states, even if there was an error
      setIsReverseLoading(false);
      setIsLoading(false);

      // Force a complete reset to ensure no visual artifacts remain
      setTimeout(() => resetAllLoadingStates(), 100);
    }
  }, [amount, symbol, positions, addNotification, refreshPositions, formatSymbol, resetAllLoadingStates, activatePanel]);

  return (
    <TradingModuleContainer
      ref={panelRef}
      key={`panel-${panelCoord.x === 0 && panelCoord.y === 0 ? 'default' : 'custom'}`}
      onMouseEnter={() => {
        setIsHovering(true);
        activatePanel();
      }}
      onMouseLeave={() => {
        setIsHovering(false);
        // Start idle timer when mouse leaves
        if (idleTimer !== null) {
          window.clearTimeout(idleTimer);
        }
        const timer = window.setTimeout(() => {
          setIsActive(false);
        }, 1000);
        setIdleTimer(timer);
      }}
      onMouseMove={resetIdleTimer}
      onClick={activatePanel}
      style={{
        // Apply positioning based on state
        ...(panelCoord.x === 0 && panelCoord.y === 0
          ? defaultPosition // Use default positioning if reset
          : { left: `${panelCoord.x}px`, top: `${panelCoord.y}px` } // Use exact positioning if dragged
        ),
        // Apply transparency when idle (but never when hovering)
        opacity: isActive || isHovering ? 1 : 0.2,
        transition: 'opacity 0.2s ease-in-out, transform 0.2s ease-out'
      }}
    >
      <TradingPanelContainer>
        <TradingPanelHeader onMouseDown={handleMouseDown}>
          <div>
            Trade {formatSymbol(symbol)}
            {availableMargin && (
              <span style={{ fontSize: '0.85em', marginLeft: '8px', opacity: 0.8 }}>
                ${parseFloat(availableMargin).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            )}
          </div>
          <div
            onClick={(e) => {
              e.stopPropagation(); // Prevent triggering drag
              resetPosition();
            }}
            style={{
              fontSize: '10px',
              opacity: 0.5,
              cursor: 'pointer',
              marginLeft: 'auto',
              padding: '2px 4px'
            }}
            title="Reset panel position"
          >
            ↺
          </div>
        </TradingPanelHeader>

        <InputContainer>
          <AmountInput
            type="text"
            value={amount}
            onChange={handleAmountChange}
            placeholder="Amount (USD)"
            aria-label="Trading amount in USD"
            disabled={isLoading}
          />
        </InputContainer>

        <ButtonsContainer>
          <ActionButton
            action="buy"
            onClick={handleBuy}
            disabled={!amount || isLoading}
            loading={isBuyLoading || false}
          >
            Buy
          </ActionButton>
          <ActionButton
            action="sell"
            onClick={handleSell}
            disabled={!amount || isLoading}
            loading={isSellLoading || false}
          >
            Sell
          </ActionButton>
          <ActionButton
            action="reverse"
            onClick={handleReverse}
            disabled={!amount || isLoading}
            loading={isReverseLoading || false}
          >
            Rev.
          </ActionButton>
        </ButtonsContainer>
      </TradingPanelContainer>

      <PositionsContainer open={showPositions}>
        {isPositionsLoading ? (
          <EmptyPositions>Loading positions...</EmptyPositions>
        ) : positions.length > 0 ? (
          <PositionsList>
            {positions.map((position, index) => (
              <PositionItem key={`${position.symbol}-${index}`} side={position.side as any}>
                <PositionDetails>
                  <PositionSymbol>
                    {formatSymbol(position.symbol)}
                    <PositionSide side={position.side as any}>
                      {position.side === EPositionSide.Long ? 'L' : 'S'}
                    </PositionSide>
                    <PositionPnl status={position.unrealizedPnl > '0' ? 'positive' : 'negative'}>{position.unrealizedPnl > '0' ? `+${position.unrealizedPnl}` : position.unrealizedPnl} usd</PositionPnl>
                  </PositionSymbol>
                  <PositionAmount>
                    {position.cryptoAmount} {position.symbol.replace('usdt', '').toLowerCase()}
                    &nbsp; (≈{position.usdAmount} usd)
                  </PositionAmount>
                </PositionDetails>
                <ActionButton
                  action="close"
                  onClick={() => handleClosePosition(position.symbol)}
                  disabled={isCloseLoading[position.symbol] || false}
                  loading={isCloseLoading[position.symbol] || false}
                >
                  Close
                </ActionButton>
              </PositionItem>
            ))}
          </PositionsList>
        ) : null}
      </PositionsContainer>
    </TradingModuleContainer>
  );
};
