import { styled } from '@stitches/react';

export const Pull<PERSON><PERSON><PERSON> = styled('div', {
  position: 'absolute',
  bottom: '-50px',
  left: '50%',
  transform: 'translateX(-50%)',
  width: '2px',
  height: '50px',
  backgroundColor: 'rgb(7, 81, 207)',
  cursor: 'grab',
  transformOrigin: 'top',
  transition: 'all 0.3s ease',

  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-10px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '15px',
    height: '15px',
    backgroundColor: 'rgb(7, 81, 207)',
    borderRadius: '50%',
    boxShadow: '0 0 5px rgba(7, 81, 207, 0.5)',
  },

  '&:active': {
    cursor: 'grabbing',
    transform: 'translateX(-50%) scaleY(1.2)',
  }
});

export const ToolboxContainer = styled('div', {
  position: 'absolute',
  left: '4%',
  top: '40%',
  transform: 'translateY(-50%)',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: '10px',
  padding: '10px',
  borderRadius: '4px',
  backgroundColor: 'rgba(255, 255, 255, 0.85)',
  border: '1px solid rgba(7, 81, 207, 0.2)',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  backdropFilter: 'blur(3px)',
  zIndex: 1000,
});

export const ToolboxItem = styled('div', {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '40px',
  height: '40px',
  borderRadius: '4px',
  cursor: 'pointer',
  transition: 'background-color 0.2s ease',
  color: 'rgb(7, 81, 207)',

  '&:hover': {
    backgroundColor: 'rgba(7, 81, 207, 0.15)',
  },

  variants: {
    active: {
      true: {
        backgroundColor: 'rgba(7, 81, 207, 0.2)',
      }
    }
  }
});

export const ToolboxTooltip = styled('div', {
  position: 'absolute',
  left: '100%',
  top: '50%',
  transform: 'translateY(-50%) translateX(-10px)',
  marginLeft: '15px',
  padding: '5px 8px',
  // backgroundColor: 'rgba(255, 255, 255, 0.9)',
  borderRadius: '4px',
  fontSize: '12px',
  fontWeight: 'bold',
  color: 'rgb(7, 81, 207)',
  // border: '1px solid rgba(7, 81, 207, 0.2)',
  pointerEvents: 'none',
  opacity: 0,
  transition: 'all 0.2s ease',
  whiteSpace: 'nowrap',
  zIndex: 1001,
});
